{"name": "moe-downloader", "version": "1.0.0", "description": "", "main": "dist/electron/app.js", "scripts": {"dev": "concurrently --kill-others \"npm run dev:renderer\" \"npm run dev:electron\" \"cd electron && tsc -w\" \"node scripts/watch-js-files.js\"", "package": "npm run build:renderer && npm run build:electron && electron-builder", "build:electron": "cd electron && tsc && cd .. && node scripts/copy-js-files.js", "build:renderer": "cd renderer && npm run build", "dev:renderer": "cd renderer && npm run dev", "dev:electron": "npm run build:electron && electron .", "copy-js": "node scripts/copy-js-files.js", "format": "prettier --write ."}, "build": {"appId": "com.moedownloader.app", "productName": "MoeDownloader", "directories": {"output": "build"}, "files": ["dist/**/*", "package.json"], "win": {"target": "nsis", "icon": "path/to/your/icon/icon.ico"}, "mac": {"target": "dmg", "icon": "path/to/your/icon/icon.icns"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "repository": {"type": "git", "url": "git+https://github.com/tlaceby/electron-svelte.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/tlaceby/electron-svelte/issues"}, "homepage": "https://github.com/tlaceby/electron-svelte#readme", "devDependencies": {"@types/better-sqlite3": "^7.6.13", "concurrently": "^8.2.2", "electron": "^28.2.3", "electron-builder": "^24.9.1", "prettier": "^3.2.5", "prettier-plugin-svelte": "^3.2.1"}, "dependencies": {"@tanstack/table-core": "^8.21.3", "axios": "^1.11.0", "better-sqlite3": "^12.2.0", "electron-reload": "^2.0.0-alpha.1", "node-cron": "^4.2.1", "webtorrent": "^2.6.10", "xml2js": "^0.6.2"}}